"""
认知评估数据模型
"""

from typing import Dict
from dataclasses import dataclass


@dataclass
class MMSEData:
    """MMSE评估数据模型"""
    
    orientation: int = 0          # 定向力 (0-10)
    instant_memory: int = 0       # 瞬时记忆 (0-3)
    calculation: int = 0          # 计算力 (0-5)
    short_term_memory: int = 0    # 短时记忆 (0-3)
    naming: int = 0               # 命名 (0-2)
    repetition: int = 0           # 复述 (0-1)
    reading: int = 0              # 阅读 (0-1)
    execution: int = 0            # 执行 (0-3)
    writing: int = 0              # 书写 (0-1)
    structure_imitation: int = 0  # 结构模仿 (0-1)
    
    @property
    def total_score(self) -> int:
        """计算总分"""
        return (
            self.orientation + self.instant_memory + self.calculation +
            self.short_term_memory + self.naming + self.repetition +
            self.reading + self.execution + self.writing + self.structure_imitation
        )
    
    @classmethod
    def from_dict(cls, data: Dict[str, int]) -> "MMSEData":
        """从字典创建MMSEData对象"""
        mmse = cls()
        mmse.orientation = data.get("定向力", 0)
        mmse.instant_memory = data.get("瞬时记忆", 0)
        mmse.calculation = data.get("计算力", 0)
        mmse.short_term_memory = data.get("短时记忆", 0)
        mmse.naming = data.get("命名", 0)
        mmse.repetition = data.get("复述", 0)
        mmse.reading = data.get("阅读", 0)
        mmse.execution = data.get("执行", 0)
        mmse.writing = data.get("书写", 0)
        mmse.structure_imitation = data.get("结构模仿", 0)
        return mmse
    
    def to_dict(self) -> Dict[str, int]:
        """转换为字典格式"""
        return {
            "定向力": self.orientation,
            "瞬时记忆": self.instant_memory,
            "计算力": self.calculation,
            "短时记忆": self.short_term_memory,
            "命名": self.naming,
            "复述": self.repetition,
            "阅读": self.reading,
            "执行": self.execution,
            "书写": self.writing,
            "结构模仿": self.structure_imitation,
        }


@dataclass
class MoCAData:
    """MoCA评估数据模型"""
    
    visuospatial_execution: int = 0  # 视空间/执行 (0-2)
    clock_drawing: int = 0           # 画钟 (0-3)
    naming: int = 0                  # 命名 (0-3)
    attention: int = 0               # 注意力 (0-3)
    calculation: int = 0             # 计算力 (0-3)
    language: int = 0                # 语言 (0-3)
    abstraction: int = 0             # 抽象能力 (0-2)
    delayed_recall: int = 0          # 延迟记忆 (0-5)
    orientation: int = 0             # 定向力 (0-6)
    
    @property
    def total_score(self) -> int:
        """计算总分"""
        return (
            self.visuospatial_execution + self.clock_drawing + self.naming +
            self.attention + self.calculation + self.language +
            self.abstraction + self.delayed_recall + self.orientation
        )
    
    @classmethod
    def from_dict(cls, data: Dict[str, int]) -> "MoCAData":
        """从字典创建MoCAData对象"""
        moca = cls()
        moca.visuospatial_execution = data.get("视空间/执行", 0)
        moca.clock_drawing = data.get("画钟", 0)
        moca.naming = data.get("命名", 0)
        moca.attention = data.get("注意力", 0)
        moca.calculation = data.get("计算力", 0)
        moca.language = data.get("语言", 0)
        moca.abstraction = data.get("抽象能力", 0)
        moca.delayed_recall = data.get("延迟记忆", 0)
        moca.orientation = data.get("定向力", 0)
        return moca
    
    def to_dict(self) -> Dict[str, int]:
        """转换为字典格式"""
        return {
            "视空间/执行": self.visuospatial_execution,
            "画钟": self.clock_drawing,
            "命名": self.naming,
            "注意力": self.attention,
            "计算力": self.calculation,
            "语言": self.language,
            "抽象能力": self.abstraction,
            "延迟记忆": self.delayed_recall,
            "定向力": self.orientation,
        }


@dataclass
class AssessmentData:
    """完整的评估数据模型"""
    
    mmse: MMSEData
    moca: MoCAData
    
    def __post_init__(self):
        """初始化后的处理"""
        if isinstance(self.mmse, dict):
            self.mmse = MMSEData.from_dict(self.mmse)
        if isinstance(self.moca, dict):
            self.moca = MoCAData.from_dict(self.moca)
    
    @property
    def mmse_total_score(self) -> int:
        """MMSE总分"""
        return self.mmse.total_score
    
    @property
    def moca_total_score(self) -> int:
        """MoCA总分"""
        return self.moca.total_score
    
    @classmethod
    def from_dict(cls, data: Dict) -> "AssessmentData":
        """从字典创建AssessmentData对象"""
        mmse_data = MMSEData.from_dict(data.get("mmse", {}))
        moca_data = MoCAData.from_dict(data.get("moca", {}))
        return cls(mmse=mmse_data, moca=moca_data)
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "mmse": self.mmse.to_dict(),
            "moca": self.moca.to_dict(),
            "mmse_total": self.mmse_total_score,
            "moca_total": self.moca_total_score,
        }
    
    def is_valid(self) -> bool:
        """验证评估数据是否完整有效"""
        return self.mmse.total_score >= 0 and self.moca.total_score >= 0