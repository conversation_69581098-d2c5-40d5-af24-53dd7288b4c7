"""
MVP架构 - 用户信息Presenter
"""

from typing import Any, Dict, Optional, TYPE_CHECKING
from ..interfaces.presenter_interfaces import IHomePresenter
from ..models.user_info import UserInfo

if TYPE_CHECKING:
    from ..interfaces.view_interfaces import IHomeView


class HomePresenter(IHomePresenter):
    """用户信息Presenter - 处理用户信息相关业务逻辑"""
    
    def __init__(self):
        """初始化Presenter"""
        self._view: Optional["IHomeView"] = None
        self._user_info: Optional[UserInfo] = None
    
    def set_view(self, view: "IHomeView") -> None:
        """设置关联的View"""
        self._view = view
    
    def validate_user_info(self, user_data: Dict[str, Any]) -> bool:
        """
        验证用户信息
        
        Args:
            user_data: 用户信息字典
            
        Returns:
            验证是否通过
        """
        try:
            user_info = UserInfo.from_dict(user_data)
            return user_info.is_valid()
        except Exception:
            return False
    
    def save_user_info(self, user_data: Dict[str, Any]) -> None:
        """
        保存用户信息
        
        Args:
            user_data: 用户信息字典
        """
        try:
            self._user_info = UserInfo.from_dict(user_data)
        except Exception as e:
            raise ValueError(f"保存用户信息失败: {e}")
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        获取已保存的用户信息
        
        Returns:
            用户信息字典，如果没有保存则返回None
        """
        if self._user_info is None:
            return None
        return self._user_info.to_dict()
    
    def clear_user_info(self) -> None:
        """清除用户信息"""
        self._user_info = None
    
    def get_validation_errors(self, user_data: Dict[str, Any]) -> Dict[str, str]:
        """
        获取验证错误的详细信息
        
        Args:
            user_data: 用户信息字典
            
        Returns:
            字段名到错误信息的映射
        """
        errors = {}
        
        # 验证姓名
        name = user_data.get("name", "").strip()
        if not name:
            errors["name"] = "请输入有效的姓名"
        
        # 验证教育程度
        education = user_data.get("education", "")
        if not education or education == "请选择教育程度":
            errors["education"] = "请选择教育程度"
        
        # 验证性别
        gender = user_data.get("gender")
        if gender is None:
            errors["gender"] = "请选择性别"
        
        # 验证出生日期
        birth_date = user_data.get("birth_date", "")
        if not birth_date:
            errors["birth_date"] = "请选择出生日期"
        
        return errors
    
    def handle_form_submit(self, user_data: Dict[str, Any]) -> bool:
        """
        处理表单提交
        
        Args:
            user_data: 用户信息字典
            
        Returns:
            是否处理成功
        """
        # 基本验证：检查必填字段
        if not user_data.get("name", "").strip():
            if self._view:
                self._view.show_error("请输入姓名")
            return False
        
        if not user_data.get("birth_date"):
            if self._view:
                self._view.show_error("请选择出生日期")
            return False
        
        if not user_data.get("education") or user_data["education"] == "请选择教育程度":
            if self._view:
                self._view.show_error("请选择教育程度")
            return False
        
        if user_data.get("gender") is None:
            if self._view:
                self._view.show_error("请选择性别")
            return False

        # 详细验证
        if not self.validate_user_info(user_data):
            errors = self.get_validation_errors(user_data)
            error_message = "\n".join(errors.values())
            if self._view:
                self._view.show_error(error_message)
            return False
        
        # 保存数据
        try:
            self.save_user_info(user_data)
            return True
        except Exception as e:
            if self._view:
                self._view.show_error(f"保存失败: {e}")
            return False
