"""
主页页面 - 纯视图层，仅负责UI和用户交互
"""

from pathlib import Path
from typing import Any, Dict, Optional

from PySide6.QtCore import QDate, Qt, Signal
from PySide6.QtGui import QFont, QKeyEvent, QPixmap, QResizeEvent
from PySide6.QtWidgets import (
    QButtonGroup,
    QComboBox,
    QDateEdit,
    QFrame,
    QLabel,
    QLineEdit,
    QMessageBox,
    QPushButton,
    QRadioButton,
    QVBoxLayout,
    QWidget,
)


class HomePage(QWidget):
    """主页页面 - 纯视图层"""

    # 定义信号
    page_finished = Signal(object)  # 页面完成信号，可传递数据
    validation_error = Signal(str)  # 验证错误信号

    def __init__(self, parent: QWidget | None = None, viewmodel=None) -> None:
        """
        初始化主页

        Args:
            parent: 父组件
            viewmodel: ViewModel实例，用于数据验证
        """
        super().__init__(parent)
        
        # 保存ViewModel引用
        self.viewmodel = viewmodel

        # 设置基本属性
        self.setStyleSheet("background-color: #f5f5f5;")

        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # 表单控件
        self.entry_name: QLineEdit | None = None
        self.entry_birth: QDateEdit | None = None
        self.combo_education: QComboBox | None = None
        self.radio_male: QRadioButton | None = None
        self.radio_female: QRadioButton | None = None
        self.gender_group: QButtonGroup | None = None
        self.start_button: QPushButton | None = None
        self.bg_label: QLabel | None = None

        # 创建界面元素
        self._create_widgets()

    def _create_widgets(self) -> None:
        """创建界面元素（背景大图，右侧表单）"""
        # 创建背景标签（铺满整个页面）
        self.bg_label = QLabel(self)
        self.bg_label.setStyleSheet("background-color: black;")
        self.bg_label.setScaledContents(True)

        # 加载背景图片
        self._load_background_image()

        # 创建右侧表单卡片（使用布局管理器）
        self._create_form_card()

    def _load_background_image(self) -> None:
        """加载背景图片"""
        try:
            bg_path = Path("info_entry.png")
            if bg_path.exists() and self.bg_label:
                # 加载图片
                pixmap = QPixmap(str(bg_path))
                self.bg_label.setPixmap(pixmap)
                print("✅ 背景图片加载成功")
            else:
                print("⚠️  背景图片未找到，使用黑色背景")
                if self.bg_label:
                    self.bg_label.setStyleSheet("background-color: black;")
        except Exception as e:
            print(f"❌ 背景图片加载失败: {e}")
            if self.bg_label:
                self.bg_label.setStyleSheet("background-color: black;")

    def _create_form_card(self) -> None:
        """创建右侧表单卡片"""
        # 创建卡片框架
        self.card_frame = QFrame(self)
        self.card_frame.setFixedSize(420, 420)
        self.card_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #b5d3f5;
                border-radius: 8px;
            }
        """)

        # 创建标题
        title = QLabel("信息录入", self.card_frame)
        title.setGeometry(32, 28, 200, 30)
        title.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        title.setStyleSheet("color: black; background: transparent; border: none;")

        # 蓝色下划线
        underline = QFrame(self.card_frame)
        underline.setGeometry(32, 62, 32, 3)
        underline.setStyleSheet("background-color: #409eff;")

        # 副标题
        subtitle = QLabel("WELCOME TO ENTER YOUR INFORMATION", self.card_frame)
        subtitle.setGeometry(32, 70, 350, 20)
        subtitle.setFont(QFont("Arial", 10))
        subtitle.setStyleSheet("color: #888888; background: transparent; border: none;")

        # 表单字段
        self._create_form_fields(self.card_frame)

        # 开始按钮
        self._create_start_button(self.card_frame)

    def _create_form_fields(self, parent: QFrame) -> None:
        """创建表单字段"""
        label_font = QFont("Arial", 12)
        entry_font = QFont("Arial", 12)

        y_start = 110
        y_gap = 48

        # 姓名
        name_label = QLabel("姓    名", parent)
        name_label.setGeometry(32, y_start, 80, 25)
        name_label.setFont(label_font)
        name_label.setStyleSheet("color: black; background: transparent; border: none;")

        self.entry_name = QLineEdit(parent)
        self.entry_name.setGeometry(120, y_start - 2, 220, 28)
        self.entry_name.setFont(entry_font)
        self.entry_name.setPlaceholderText("请输入姓名")
        self.entry_name.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                padding: 4px;
                background: white;
            }
            QLineEdit:focus {
                border: 1px solid #409eff;
            }
        """)

        # 出生日期
        birth_label = QLabel("出生日期", parent)
        birth_label.setGeometry(32, y_start + y_gap, 80, 25)
        birth_label.setFont(label_font)
        birth_label.setStyleSheet(
            "color: black; background: transparent; border: none;"
        )

        self.entry_birth = QDateEdit(parent)
        self.entry_birth.setGeometry(120, y_start + y_gap - 2, 220, 28)
        self.entry_birth.setFont(entry_font)
        self.entry_birth.setDate(None)
        self.entry_birth.setDisplayFormat("yyyy-MM-dd")
        self.entry_birth.setStyleSheet("""
            QDateEdit {
                border: 1px solid #ccc;
                padding: 4px;
                background: white;
            }
            QDateEdit:focus {
                border: 1px solid #409eff;
            }
        """)
        self.entry_birth.setCalendarPopup(True)
        # 优化弹出日历框样式
        calendar = self.entry_birth.calendarWidget()
        calendar.setStyleSheet("""
            QCalendarWidget QWidget {
                background-color: #f8fafc;
                color: #222;
                font-size: 12px;
            }
            QCalendarWidget QAbstractItemView:enabled {
                background: #f8fafc;
                selection-background-color: #409eff;
                selection-color: #fff;
                border-radius: 4px;
                outline: none;
            }
            QCalendarWidget QToolButton {
                background: #409eff;
                color: white;
                border: none;
                border-radius: 4px;
                margin: 2px;
                padding: 4px 8px;
                font-size: 12px;
                min-width: 40px;
            }
            QCalendarWidget QToolButton:hover {
                background: #66b1ff;
            }
            QCalendarWidget QToolButton::menu-indicator,
            QCalendarWidget QToolButton::down-arrow {
                width: 0px; height: 0px;
            }
            QCalendarWidget QSpinBox {
                background: #fff;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 2px 4px;
                font-size: 12px;
            }
            QCalendarWidget QHeaderView::section {
                background-color: #f0f4fa;
                color: #222;
                font-weight: bold;
                border: none;
                border-bottom: 1px solid #e0e6ed;
                padding: 4px 0;
            }
            QCalendarWidget QAbstractItemView {
                selection-background-color: #409eff;
                selection-color: #fff;
            }
            QCalendarWidget QAbstractItemView:disabled {
                color: #ccc;
            }
            QCalendarWidget QAbstractItemView:enabled:item:selected {
                background: #409eff;
                color: #fff;
            }
            QCalendarWidget QAbstractItemView:enabled:item:hover {
                background: #e6f2ff;
                color: #222;
            }
        """)
        calendar.setMaximumWidth(260)
        calendar.setMaximumHeight(260)
        self.entry_birth.lineEdit().setReadOnly(True)
        self.entry_birth.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        self.entry_birth.setButtonSymbols(QDateEdit.ButtonSymbols.NoButtons)

        # 教育程度
        edu_label = QLabel("教育程度", parent)
        edu_label.setGeometry(32, y_start + 2 * y_gap, 80, 25)
        edu_label.setFont(label_font)
        edu_label.setStyleSheet("color: black; background: transparent; border: none;")

        self.combo_education = QComboBox(parent)
        self.combo_education.setGeometry(120, y_start + 2 * y_gap - 2, 220, 28)
        self.combo_education.setFont(entry_font)
        self.combo_education.addItems(
            ["请选择教育程度", "小学", "初中", "高中", "大专", "本科", "硕士", "博士"]
        )
        self.combo_education.setStyleSheet("""
            QComboBox {
                border: 1px solid #ccc;
                padding: 4px;
                background: white;
                color: #333333;
                selection-background-color: #409eff;
                selection-color: white;
            }
            QComboBox:focus {
                border: 1px solid #409eff;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
        """)

        # 性别
        gender_label = QLabel("性    别", parent)
        gender_label.setGeometry(32, y_start + 3 * y_gap, 80, 25)
        gender_label.setFont(label_font)
        gender_label.setStyleSheet(
            "color: black; background: transparent; border: none;"
        )

        self.radio_male = QRadioButton("男", parent)
        self.radio_male.setGeometry(120, y_start + 3 * y_gap, 60, 25)
        self.radio_male.setFont(entry_font)
        self.radio_male.setStyleSheet(
            "color: black; background: transparent; border: none;"
        )

        self.radio_female = QRadioButton("女", parent)
        self.radio_female.setGeometry(200, y_start + 3 * y_gap, 60, 25)
        self.radio_female.setFont(entry_font)
        self.radio_female.setStyleSheet("color: #333333; background: transparent;")

        # 创建按钮组
        self.gender_group = QButtonGroup()
        self.gender_group.addButton(self.radio_male, 0)
        self.gender_group.addButton(self.radio_female, 1)

    def _create_start_button(self, parent: QFrame) -> None:
        """创建开始按钮"""
        self.start_button = QPushButton("开始实验", parent)
        self.start_button.setGeometry(120, 340, 200, 40)
        self.start_button.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #409eff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #66b1ff;
            }
            QPushButton:pressed {
                background-color: #3a8ee6;
            }
        """)
        self.start_button.clicked.connect(self._on_start_clicked)

    def _on_start_clicked(self) -> None:
        """处理开始按钮点击"""
        # 获取表单数据
        user_info = self.get_form_data()
        
        # 基本验证：检查必填字段
        if not user_info.get("name", "").strip():
            self.show_error("请输入姓名")
            return
        
        if not user_info.get("birth_date"):
            self.show_error("请选择出生日期")
            return
        
        if not user_info.get("education") or user_info["education"] == "请选择教育程度":
            self.show_error("请选择教育程度")
            return
        
        if user_info.get("gender") is None:
            self.show_error("请选择性别")
            return

        # 使用ViewModel进行详细验证
        if self.viewmodel:
            if not self.viewmodel.validate_user_info(user_info):
                errors = self.viewmodel.get_validation_errors(user_info)
                error_message = "\n".join(errors.values())
                self.show_error(error_message)
                return
        else:
            print("⚠️ ViewModel未设置，跳过详细验证")

        # 发送页面完成信号
        self.page_finished.emit(user_info)

    def clear_form(self) -> None:
        """清空表单内容"""
        print("🔄 清空用户信息表单")
        if self.entry_name:
            self.entry_name.clear()
        if self.entry_birth:
            self.entry_birth.setDate(None)
        if self.combo_education:
            self.combo_education.setCurrentIndex(0)
        if self.gender_group:
            self.gender_group.setExclusive(False)
            if self.radio_male:
                self.radio_male.setChecked(False)
            if self.radio_female:
                self.radio_female.setChecked(False)
            self.gender_group.setExclusive(True)

    def restore_form(self, user_info: dict) -> None:
        """恢复表单数据"""
        print("🔄 恢复用户信息表单数据")
        print(f"📝 恢复用户信息: {user_info}")

        try:
            # 恢复姓名
            if self.entry_name and "name" in user_info:
                self.entry_name.setText(user_info["name"])

            # 恢复出生日期
            if self.entry_birth and "birth_date" in user_info:
                try:
                    birth_date = QDate.fromString(user_info["birth_date"], "yyyy-MM-dd")
                    if birth_date.isValid():
                        self.entry_birth.setDate(birth_date)
                    else:
                        print("⚠️ 出生日期格式无效")
                except Exception as e:
                    print(f"⚠️ 设置出生日期失败: {e}")

            # 恢复教育程度
            if self.combo_education and "education" in user_info:
                education = user_info["education"]
                # 查找匹配的教育程度选项
                for i in range(self.combo_education.count()):
                    if self.combo_education.itemText(i) == education:
                        self.combo_education.setCurrentIndex(i)
                        break

            # 恢复性别
            if self.gender_group and "gender" in user_info:
                gender = user_info["gender"]
                self.gender_group.setExclusive(False)
                if gender is True and self.radio_male:
                    self.radio_male.setChecked(True)
                elif gender is False and self.radio_female:
                    self.radio_female.setChecked(True)
                self.gender_group.setExclusive(True)

            print("✅ 表单数据恢复完成")

        except Exception as e:
            print(f"❌ 恢复表单数据失败: {e}")

    def get_form_data(self) -> Dict[str, Any]:
        """获取表单数据"""
        return {
            "name": self.entry_name.text() if self.entry_name else "",
            "birth_date": self.entry_birth.date().toString("yyyy-MM-dd")
            if self.entry_birth
            else "",
            "education": self.combo_education.currentText()
            if self.combo_education
            else "",
            "gender": self.radio_male.isChecked()
            if self.radio_male
            else self.radio_female.isChecked()
            if self.radio_female
            else None,
        }

    def show_error(self, message: str) -> None:
        """显示错误消息"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("输入错误")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Warning)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
                color: black;
            }
            QMessageBox QLabel {
                background-color: white;
                color: #333333;
            }
            QMessageBox QPushButton {
                background-color: #409eff;
                color: white;
                border: none;
                padding: 6px 20px;
                border-radius: 4px;
                min-width: 60px;
            }
        """)
        msg_box.exec()

    def keyPressEvent(self, event: QKeyEvent) -> None:
        """处理按键事件"""
        # 回车键提交表单
        if event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            self._on_start_clicked()
        super().keyPressEvent(event)

    def resizeEvent(self, event: QResizeEvent) -> None:
        """窗口大小改变时的处理"""
        super().resizeEvent(event)

        # 调整背景图片大小
        if self.bg_label:
            self.bg_label.setGeometry(0, 0, self.width(), self.height())

        # 调整表单卡片位置（右侧居中）
        if hasattr(self, "card_frame") and self.card_frame:
            # 计算右侧区域的中心位置
            window_width = self.width()
            window_height = self.height()

            # 右侧60%区域的中心
            right_area_start = int(window_width * 0.6)
            right_area_width = window_width - right_area_start

            card_x = right_area_start + (right_area_width - 420) // 2
            card_y = (window_height - 420) // 2

            # 确保不超出边界
            card_x = max(right_area_start + 20, card_x)
            card_y = max(20, card_y)

            self.card_frame.move(card_x, card_y)
