"""
HomePage ViewModel - 处理用户信息相关业务逻辑
"""

from typing import Any, Dict, Optional
from ..models.user_info import UserInfo


class HomeViewModel:
    """HomePage的ViewModel，处理用户信息的业务逻辑"""
    
    def __init__(self):
        """初始化ViewModel"""
        self._user_info: Optional[UserInfo] = None
    
    def validate_user_info(self, user_data: Dict[str, Any]) -> bool:
        """
        验证用户信息
        
        Args:
            user_data: 用户信息字典
            
        Returns:
            验证是否通过
        """
        try:
            user_info = UserInfo.from_dict(user_data)
            return user_info.is_valid()
        except Exception:
            return False
    
    def save_user_info(self, user_data: Dict[str, Any]) -> None:
        """
        保存用户信息
        
        Args:
            user_data: 用户信息字典
        """
        try:
            self._user_info = UserInfo.from_dict(user_data)
        except Exception as e:
            raise ValueError(f"保存用户信息失败: {e}")
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        获取已保存的用户信息
        
        Returns:
            用户信息字典，如果没有保存则返回None
        """
        if self._user_info is None:
            return None
        return self._user_info.to_dict()
    
    def clear_user_info(self) -> None:
        """清除用户信息"""
        self._user_info = None
    
    def get_validation_errors(self, user_data: Dict[str, Any]) -> Dict[str, str]:
        """
        获取验证错误的详细信息
        
        Args:
            user_data: 用户信息字典
            
        Returns:
            字段名到错误信息的映射
        """
        errors = {}
        
        # 验证姓名
        name = user_data.get("name", "").strip()
        if not name:
            errors["name"] = "请输入有效的姓名"
        
        # 验证教育程度
        education = user_data.get("education", "")
        if not education or education == "请选择教育程度":
            errors["education"] = "请选择教育程度"
        
        # 验证性别
        gender = user_data.get("gender")
        if gender is None:
            errors["gender"] = "请选择性别"
        
        # 验证出生日期
        birth_date = user_data.get("birth_date", "")
        if not birth_date:
            errors["birth_date"] = "请选择出生日期"
        
        return errors