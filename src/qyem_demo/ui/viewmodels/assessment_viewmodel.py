"""
AssessmentPage ViewModel - 处理认知评估相关业务逻辑
"""

from typing import Any, Dict, Optional, List
from ..models.assessment_data import AssessmentData, MMSEData, MoCAData


class AssessmentViewModel:
    """AssessmentPage的ViewModel，处理认知评估的业务逻辑"""

    def __init__(self):
        """初始化ViewModel"""
        self._assessment_data: Optional[AssessmentData] = None

        # MMSE评估项目配置
        self.mmse_items = [
            {"name": "orientation", "label": "定向力", "max_score": 10},
            {"name": "instant_memory", "label": "瞬时记忆", "max_score": 3},
            {"name": "calculation", "label": "计算力", "max_score": 5},
            {"name": "short_term_memory", "label": "短时记忆", "max_score": 3},
            {"name": "naming", "label": "命名", "max_score": 2},
            {"name": "repetition", "label": "复述", "max_score": 1},
            {"name": "reading", "label": "阅读", "max_score": 1},
            {"name": "execution", "label": "执行", "max_score": 3},
            {"name": "writing", "label": "书写", "max_score": 1},
            {"name": "structure_imitation", "label": "结构模仿", "max_score": 1},
        ]

        # MoCA评估项目配置
        self.moca_items = [
            {"name": "visuospatial_execution", "label": "视空间/执行", "max_score": 2},
            {"name": "clock_drawing", "label": "画钟", "max_score": 3},
            {"name": "naming", "label": "命名", "max_score": 3},
            {"name": "attention", "label": "注意力", "max_score": 3},
            {"name": "calculation", "label": "计算力", "max_score": 3},
            {"name": "language", "label": "语言", "max_score": 3},
            {"name": "abstraction", "label": "抽象能力", "max_score": 2},
            {"name": "delayed_recall", "label": "延迟记忆", "max_score": 5},
            {"name": "orientation", "label": "定向力", "max_score": 6},
        ]

    def validate_assessment_data(
        self, mmse_data: Dict[str, int], moca_data: Dict[str, int]
    ) -> bool:
        """
        验证评估数据

        Args:
            mmse_data: MMSE评估数据
            moca_data: MoCA评估数据

        Returns:
            验证是否通过
        """
        try:
            # 验证MMSE数据
            for item_config in self.mmse_items:
                item_name = item_config["name"]
                max_score = item_config["max_score"]
                score = mmse_data.get(item_name)  # 不使用默认值-1

                # 检查字段是否存在
                if score is None:
                    return False  # 字段缺失，验证失败

                # 检查数据类型
                if not isinstance(score, int):
                    return False  # 类型错误，验证失败

                # 检查数值范围
                if not (0 <= score <= max_score):
                    return False  # 范围错误，验证失败

            # 验证MoCA数据
            for item_config in self.moca_items:
                item_name = item_config["name"]
                max_score = item_config["max_score"]
                score = moca_data.get(item_name)  # 不使用默认值-1

                # 检查字段是否存在
                if score is None:
                    return False  # 字段缺失，验证失败

                # 检查数据类型
                if not isinstance(score, int):
                    return False  # 类型错误，验证失败

                # 检查数值范围
                if not (0 <= score <= max_score):
                    return False  # 范围错误，验证失败

            return True
        except Exception:
            return False

    def save_assessment_data(
        self, mmse_data: Dict[str, int], moca_data: Dict[str, int]
    ) -> None:
        """
        保存评估数据

        Args:
            mmse_data: MMSE评估数据
            moca_data: MoCA评估数据
        """
        try:
            mmse = MMSEData.from_dict(mmse_data)
            moca = MoCAData.from_dict(moca_data)
            self._assessment_data = AssessmentData(mmse=mmse, moca=moca)
        except Exception as e:
            raise ValueError(f"保存评估数据失败: {e}")

    def get_assessment_data(self) -> Optional[Dict[str, Any]]:
        """
        获取已保存的评估数据

        Returns:
            评估数据字典，如果没有保存则返回None
        """
        if self._assessment_data is None:
            return None
        return self._assessment_data.to_dict()

    def clear_assessment_data(self) -> None:
        """清除评估数据"""
        self._assessment_data = None

    def get_validation_errors(
        self,
        mmse_data: Dict[str, int],
        moca_data: Dict[str, int],
        only_one_error: bool = False,
    ) -> Dict[str, str]:
        """
        获取验证错误的详细信息

        Args:
            mmse_data: MMSE评估数据
            moca_data: MoCA评估数据
            only_one_error: 是否只返回一个错误
        Returns:
            字段名到错误信息的映射
        """
        errors = {}

        # 验证MMSE数据
        for item_config in self.mmse_items:
            item_name = item_config["name"]
            max_score = item_config["max_score"]
            score = mmse_data.get(item_name)
            if score is None:
                errors[f"mmse_{item_name}"] = f"{item_name}分值不能为空"
            elif not isinstance(score, int):
                errors[f"mmse_{item_name}"] = f"{item_name}必须是整数"
            elif not (0 <= score <= max_score):
                errors[f"mmse_{item_name}"] = (
                    f"{item_name}分值必须在 0-{max_score} 之间"
                )
            if only_one_error and errors:
                return errors

        # 验证MoCA数据
        for item_config in self.moca_items:
            item_name = item_config["name"]
            max_score = item_config["max_score"]
            score = moca_data.get(item_name)

            if score is None:
                errors[f"moca_{item_name}"] = f"{item_name}分值不能为空"
            elif not isinstance(score, int):
                errors[f"moca_{item_name}"] = f"{item_name}必须是整数"
            elif not (0 <= score <= max_score):
                errors[f"moca_{item_name}"] = (
                    f"{item_name}分值必须在 0-{max_score} 之间"
                )
            if only_one_error and errors:
                return errors
        return errors

    def get_mmse_items(self) -> List[Dict[str, Any]]:
        """获取MMSE评估项目配置"""
        return self.mmse_items.copy()

    def get_moca_items(self) -> List[Dict[str, Any]]:
        """获取MoCA评估项目配置"""
        return self.moca_items.copy()
