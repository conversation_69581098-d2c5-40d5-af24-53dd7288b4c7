# UI架构重构说明

## 概述

本次重构将原有的混合架构转换为清晰的三层架构模式，实现了视图层与业务逻辑层的完全分离。

## 架构层次

### 1. 视图层 (View Layer)
- **职责**: 仅负责UI展示和用户交互
- **组件**:
  - `HomePage`: 用户信息录入界面
  - `AssessmentPage`: 认知评估界面
- **特点**:
  - 不再包含任何业务逻辑
  - 通过信号与ViewModel通信
  - 实现了`HomeViewInterface`和`AssessmentViewInterface`接口

### 2. 业务逻辑层 (ViewModel Layer)
- **职责**: 处理业务逻辑、数据验证和管理
- **组件**:
  - `HomeViewModel`: 用户信息业务逻辑
  - `AssessmentViewModel`: 认知评估业务逻辑
- **特点**:
  - 包含数据验证逻辑
  - 管理应用状态
  - 与数据模型直接交互

### 3. 数据模型层 (Model Layer)
- **职责**: 定义数据结构
- **组件**:
  - `UserInfo`: 用户信息数据模型
  - `MMSEData`: MMSE评估数据模型
  - `MoCAData`: MoCA评估数据模型
  - `AssessmentData`: 完整评估数据模型

### 4. 接口层 (Interface Layer)
- **职责**: 定义各层之间的通信协议
- **组件**:
  - `HomeViewInterface`: HomePage视图接口
  - `AssessmentViewInterface`: AssessmentPage视图接口
  - `HomeViewModelInterface`: HomeViewModel接口
  - `AssessmentViewModelInterface`: AssessmentViewModel接口

## 目录结构

```
src/qyem_demo/ui/
├── __init__.py
├── main_window.py          # 主窗口，负责协调各层
├── pages/                  # 视图层
│   ├── __init__.py
│   ├── home_page.py        # 纯视图 - HomePage
│   └── assessment_page.py  # 纯视图 - AssessmentPage
├── viewmodels/             # 业务逻辑层
│   ├── __init__.py
│   ├── home_viewmodel.py   # HomePage业务逻辑
│   └── assessment_viewmodel.py  # AssessmentPage业务逻辑
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── user_info.py        # 用户信息数据模型
│   └── assessment_data.py  # 评估数据模型
├── interfaces/             # 接口定义
│   ├── __init__.py
│   ├── view_interfaces.py  # 视图接口
│   └── viewmodel_interfaces.py  # ViewModel接口
└── playback_window.py      # 播放窗口（保持不变）
```

## 通信流程

### 用户信息录入流程
1. **HomePage** 收集用户输入并发送信号
2. **MainWindow** 接收信号并调用 **HomeViewModel** 验证
3. **HomeViewModel** 验证数据并保存
4. **MainWindow** 根据结果切换页面或显示错误

### 认知评估流程
1. **AssessmentPage** 收集评估数据并发送信号
2. **MainWindow** 接收信号并调用 **AssessmentViewModel** 验证
3. **AssessmentViewModel** 验证数据并保存
4. **MainWindow** 根据结果继续流程或显示错误

## 优势

1. **单一职责**: 每个层次都有明确的职责边界
2. **可测试性**: ViewModel层可以独立测试
3. **可维护性**: 修改UI不影响业务逻辑，反之亦然
4. **可扩展性**: 新增功能只需在对应层次添加代码
5. **解耦**: 各层之间通过接口通信，降低耦合度

## 使用示例

### 在MainWindow中使用ViewModels

```python
# 验证用户信息
if not self.home_viewmodel.validate_user_info(user_data):
    errors = self.home_viewmodel.get_validation_errors(user_data)
    self.home_page.show_error(error_message)
    return

# 保存用户信息
self.home_viewmodel.save_user_info(user_data)
```

### 在ViewModels中使用数据模型

```python
# 创建数据对象
user_info = UserInfo.from_dict(user_data)
if user_info.is_valid():
    self._user_info = user_info
```

## 未来扩展

该架构支持以下扩展：
- 添加新的页面和对应的ViewModel
- 替换UI框架（如从PySide6切换到其他框架）
- 添加数据持久化层
- 实现单元测试