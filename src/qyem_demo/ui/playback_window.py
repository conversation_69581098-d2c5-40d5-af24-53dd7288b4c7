"""
简化的播放窗口 - 使用新的架构设计
"""

from typing import TYPE_CHECKING

from PySide6.QtCore import QTimer, Qt, Signal
from PySide6.QtGui import QCloseEvent, QFont, QKeyEvent, QPixmap, QResizeEvent
from PySide6.QtWidgets import QLabel, QMainWindow

if TYPE_CHECKING:
    from qyem_demo.experiment_controller import ExperimentController

BACKGROUND_COLOR = "#f5f5f5"
TEXT_COLOR = "#333333"
SHOW_LABEL = False


class PlaybackWindow(QMainWindow):
    """
    简化的播放窗口
    
    职责：
    1. 纯UI显示
    2. 用户交互处理
    3. 通过信号与控制器通信
    """

    # 信号定义
    playback_finished = Signal(dict)  # 播放完成
    playback_stopped = Signal(dict)   # 播放中断
    user_exit_requested = Signal()    # 用户请求退出

    def __init__(self, experiment_controller: "ExperimentController") -> None:
        """
        初始化播放窗口

        Args:
            experiment_controller: 实验控制器
        """
        super().__init__()
        
        self.experiment_controller = experiment_controller
        
        # UI元素
        self.image_label: QLabel | None = None
        self.progress_label: QLabel | None = None
        self.exit_hint_label: QLabel | None = None
        
        # 播放状态
        self.is_playing = False
        self.slideshow_timer: QTimer | None = None
        self.current_stimulus_index = 0
        self.total_stimuli = 0
        
        # 初始化UI
        self._init_ui()
        self._connect_signals()

    def _init_ui(self) -> None:
        """初始化用户界面"""
        self.setWindowTitle("眼动数据采集 - 播放窗口")
        self.setStyleSheet(f"background-color: {BACKGROUND_COLOR};")

        # 设置窗口属性但不显示
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, False)

        # 主图片显示区域
        self.image_label = QLabel(self)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet(f"background-color: {BACKGROUND_COLOR};")
        self.image_label.setText("准备中...")  # 初始文本
        self.image_label.setScaledContents(False)  # 不缩放图片，保持原始尺寸

        # 进度显示（右下角）
        self.progress_label = QLabel("0 / 0", self)
        self.progress_label.setFont(QFont("Arial", 14))
        self.progress_label.setStyleSheet(f"color: {TEXT_COLOR}; background: transparent;")
        self.progress_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        if not SHOW_LABEL:
            self.progress_label.hide()

        # ESC键退出提示（左上角）
        self.exit_hint_label = QLabel("按ESC键返回主界面", self)
        self.exit_hint_label.setFont(QFont("Arial", 12))
        self.exit_hint_label.setStyleSheet(f"color: {TEXT_COLOR}; background: transparent;")
        if not SHOW_LABEL:
            self.exit_hint_label.hide()

        # 设置焦点
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        # 初始化定时器
        self.slideshow_timer = QTimer()
        self.slideshow_timer.setSingleShot(True)
        self.slideshow_timer.timeout.connect(self._next_stimulus)

    def _connect_signals(self) -> None:
        """连接回调"""
        # 设置实验控制器的回调
        self.experiment_controller.experiment_started_callback = self._on_experiment_started
        self.experiment_controller.experiment_finished_callback = self._on_experiment_finished
        self.experiment_controller.experiment_stopped_callback = self._on_experiment_stopped
        self.experiment_controller.stimulus_changed_callback = self._on_stimulus_changed
        self.experiment_controller.error_occurred_callback = self._on_error_occurred

    def start_playback(self, session_name: str, user_info: dict) -> bool:
        """
        开始播放

        Args:
            session_name: 会话名称
            user_info: 用户信息

        Returns:
            是否成功开始
        """
        print(f"🎬 开始播放: {session_name}")

        # 确保窗口全屏显示
        self.showFullScreen()
        self.raise_()
        self.activateWindow()

        # 通过控制器开始实验
        if self.experiment_controller.start_experiment(session_name, user_info):
            self.is_playing = True
            return True
        else:
            return False

    def stop_playback(self, save_data: bool = True, reason: str = "用户停止") -> None:
        """
        停止播放
        
        Args:
            save_data: 是否保存数据
            reason: 停止原因
        """
        if not self.is_playing:
            return
            
        print(f"⏹️ 停止播放: {reason}")
        self.is_playing = False
        
        if self.slideshow_timer:
            self.slideshow_timer.stop()
        
        # 检查数据质量（仅数据不合格时提示）
        final_save_data = save_data
        if save_data:
            try:
                # 获取当前采集的数据
                data = self.experiment_controller.collector.get_collected_data()
                if data:
                    # 检查数据质量
                    quality_result = self.experiment_controller.collector.check_data_quality()
                    
                    # 仅数据不合格时显示警告对话框
                    from qyem_demo.quality_checker import show_quality_dialog
                    user_continue = show_quality_dialog(quality_result)
                    final_save_data = user_continue
                    
                    if not user_continue:
                        print("🔄 用户选择重新采集数据")
                        # 不保存数据，但继续执行停止流程
                        final_save_data = False
                        reason = "数据质量不合格，用户选择重新采集"
                            
            except Exception as e:
                print(f"⚠️ 数据质量检查失败: {e}，继续保存数据")
                final_save_data = save_data
        
        # 通过控制器停止实验
        result = self.experiment_controller.stop_experiment(final_save_data, reason)
        
        # 发送停止信号
        if final_save_data:
            self.playback_finished.emit(result)
        else:
            self.playback_stopped.emit(result)
        
        # 隐藏窗口
        self.hide()

    def _on_experiment_started(self, session_info: dict) -> None:
        """实验开始回调"""
        self.total_stimuli = session_info.get("stimulus_count", 0)
        self.current_stimulus_index = 0
        self._update_progress()
        print(f"✅ 实验开始，共 {self.total_stimuli} 个刺激")

        # 清除初始文本
        if self.image_label:
            self.image_label.setText("加载中...")

        # 开始播放第一个刺激
        if self.total_stimuli > 0:
            print("🎬 开始播放第一个刺激")
            self.experiment_controller.set_current_stimulus(0)
        else:
            print("❌ 没有刺激可播放")
            if self.image_label:
                self.image_label.setText("没有刺激可播放")

    def _on_experiment_finished(self, result: dict) -> None:
        """实验完成回调"""
        print("✅ 实验正常完成")
        
        # 检查数据质量
        quality_result = result.get("quality_check")
        if quality_result and not quality_result["valid"]:
            try:
                from qyem_demo.quality_checker import show_quality_dialog
                user_continue = show_quality_dialog(quality_result)
                if not user_continue:
                    # 用户选择重新采集
                    print("🔄 用户选择重新采集数据")
                    # 不保存数据，直接返回重新采集状态
                    result["success"] = False
                    result["error"] = "数据质量不合格，用户选择重新采集"
                    result["retry_requested"] = True
                    self.playback_stopped.emit(result)
                    return
            except Exception as e:
                print(f"⚠️ 质量检查对话框显示失败: {e}")
        
        self.playback_finished.emit(result)

    def _on_experiment_stopped(self, result: dict) -> None:
        """实验中断回调"""
        print(f"⏹️ 实验中断: {result.get('reason', '未知原因')}")
        self.playback_stopped.emit(result)

    def _on_stimulus_changed(self, stimulus_info: dict) -> None:
        """刺激变化回调"""
        self.current_stimulus_index = stimulus_info["index"]
        self._show_stimulus(stimulus_info)
        self._update_progress()
        
        # 设置定时器切换到下一个刺激
        duration_ms = int(stimulus_info["duration"] * 1000)
        if self.slideshow_timer:
            self.slideshow_timer.start(duration_ms)

    def _on_error_occurred(self, error_message: str) -> None:
        """错误发生回调"""
        print(f"❌ 错误: {error_message}")
        # 可以在这里显示错误对话框

    def _show_stimulus(self, stimulus_info: dict) -> None:
        """显示刺激"""
        image_path = stimulus_info["path"]
        collect_status = "采集中" if stimulus_info["collect"] else "不采集"

        print(f"🖼️ 显示刺激 {stimulus_info['index'] + 1}/{stimulus_info['total']}: "
              f"{stimulus_info['name']} ({collect_status})")
        print(f"   图片路径: {image_path}")

        # 检查文件是否存在
        from pathlib import Path
        if not Path(image_path).exists():
            print(f"❌ 图片文件不存在: {image_path}")
            self._next_stimulus()
            return

        # 加载并显示图片
        pixmap = QPixmap(image_path)
        if pixmap.isNull():
            print(f"❌ 无法加载图片: {image_path}")
            self._next_stimulus()
            return

        print(f"✅ 图片加载成功，尺寸: {pixmap.width()}x{pixmap.height()}")

        if self.image_label:
            # 清除之前的文本
            self.image_label.setText("")

            # 设置图片，保持原始尺寸
            self.image_label.setPixmap(pixmap)

            # 居中显示图片
            self._center_image()

            # 强制刷新界面
            self.image_label.update()
            self.update()
            self.repaint()  # 强制重绘

            # 调试信息
            print("✅ 图片已设置到界面（原始尺寸，居中显示）")
            print(f"   图片原始尺寸: {pixmap.width()}x{pixmap.height()}")
            print(f"   窗口尺寸: {self.width()}x{self.height()}")
            print(f"   图片位置: ({self.image_label.x()}, {self.image_label.y()})")
            print(f"   窗口可见: {self.isVisible()}")
        else:
            print("❌ image_label 为空")

    def _next_stimulus(self) -> None:
        """切换到下一个刺激"""
        if not self.is_playing:
            return
            
        next_index = self.current_stimulus_index + 1
        
        # 检查是否还有更多刺激
        if next_index >= self.total_stimuli:
            # 所有刺激播放完成
            self.stop_playback(save_data=True, reason="正常完成")
        else:
            # 设置下一个刺激
            self.experiment_controller.set_current_stimulus(next_index)

    def _update_progress(self) -> None:
        """更新进度显示"""
        if self.progress_label and SHOW_LABEL:
            progress_text = f"{self.current_stimulus_index + 1} / {self.total_stimuli}"
            self.progress_label.setText(progress_text)

    def keyPressEvent(self, event: QKeyEvent) -> None:
        """处理按键事件"""
        if event.key() == Qt.Key.Key_Escape:
            print("⌨️ 用户按下ESC键")
            self._handle_user_exit()
        super().keyPressEvent(event)

    def _handle_user_exit(self) -> None:
        """处理用户退出"""
        if not self.is_playing:
            return
            
        # 检查是否有数据
        status = self.experiment_controller.get_experiment_status()
        data_count = status.get("data_count", 0)
        
        if data_count > 0:
            print(f"📊 检测到 {data_count} 个数据点，将保存数据")
            self.stop_playback(save_data=True, reason="用户中断但保存数据")
        else:
            print("📊 未检测到数据，不保存")
            self.stop_playback(save_data=False, reason="用户中断且不保存")

    def resizeEvent(self, event: QResizeEvent) -> None:
        """窗口大小改变事件"""
        super().resizeEvent(event)

        # 重新居中图片
        self._center_image()

        # 调整其他UI元素位置
        if self.progress_label and SHOW_LABEL:
            self.progress_label.resize(200, 30)
            self.progress_label.move(self.width() - 210, self.height() - 40)

        if self.exit_hint_label and SHOW_LABEL:
            self.exit_hint_label.resize(200, 30)
            self.exit_hint_label.move(10, 10)

    def _center_image(self) -> None:
        """居中显示当前图片"""
        if not self.image_label:
            return

        # 检查是否有图片
        current_pixmap = self.image_label.pixmap()
        if not current_pixmap or current_pixmap.isNull():
            return

        # 调整image_label尺寸以适应图片
        self.image_label.resize(current_pixmap.size())

        # 计算居中位置
        window_width = self.width()
        window_height = self.height()
        image_width = current_pixmap.width()
        image_height = current_pixmap.height()

        x = (window_width - image_width) // 2
        y = (window_height - image_height) // 2

        # 设置居中位置
        self.image_label.move(x, y)

    def closeEvent(self, event: QCloseEvent) -> None:
        """窗口关闭事件"""
        if self.is_playing:
            self.stop_playback(save_data=False, reason="窗口关闭")
        event.accept()
