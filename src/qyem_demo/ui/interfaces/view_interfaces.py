"""
MVP架构 - View接口定义
"""

from abc import ABC, abstractmethod
from typing import Dict, Any


class IHomeView(ABC):
    """用户信息录入页面视图接口"""
    
    @abstractmethod
    def show_error(self, message: str) -> None:
        """显示错误消息"""
        pass
    
    @abstractmethod
    def show_success(self, message: str) -> None:
        """显示成功消息"""
        pass
    
    @abstractmethod
    def get_form_data(self) -> Dict[str, Any]:
        """获取表单数据"""
        pass
    
    @abstractmethod
    def clear_form(self) -> None:
        """清空表单"""
        pass
    
    @abstractmethod
    def restore_form(self, data: Dict[str, Any]) -> None:
        """恢复表单数据"""
        pass
    
    @abstractmethod
    def set_form_enabled(self, enabled: bool) -> None:
        """设置表单是否可用"""
        pass


class IAssessmentView(ABC):
    """认知评估页面视图接口"""
    
    @abstractmethod
    def show_error(self, message: str) -> None:
        """显示错误消息"""
        pass
    
    @abstractmethod
    def show_success(self, message: str) -> None:
        """显示成功消息"""
        pass
    
    @abstractmethod
    def get_mmse_data(self) -> Dict[str, int]:
        """获取MMSE评估数据"""
        pass
    
    @abstractmethod
    def get_moca_data(self) -> Dict[str, int]:
        """获取MoCA评估数据"""
        pass
    
    @abstractmethod
    def clear_forms(self) -> None:
        """清空所有表单"""
        pass
    
    @abstractmethod
    def restore_forms(self, assessment_data: Dict[str, Any]) -> None:
        """恢复表单数据"""
        pass
    
    @abstractmethod
    def set_forms_enabled(self, enabled: bool) -> None:
        """设置表单是否可用"""
        pass
