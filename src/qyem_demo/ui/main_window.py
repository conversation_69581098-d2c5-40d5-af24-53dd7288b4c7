"""
主窗口模块 - 管理用户录入窗口
"""

from typing import TYPE_CHECKING, Optional
from datetime import datetime

from PySide6.QtGui import QCloseEvent
from PySide6.QtWidgets import QMainWindow, QMessageBox

if TYPE_CHECKING:
    from qyem_demo.eyetracker import EyeTrackerSession
    from qyem_demo.paradigm import ParadigmService

from .pages import HomePage, AssessmentPage
from .playback_window import PlaybackWindow
from .presenters import HomePresenter, AssessmentPresenter, ExperimentPresenter


class MainWindow(QMainWindow):
    """主窗口类 - 用户录入界面"""

    def __init__(
        self,
        dry_run: bool = False,
        et_session: Optional["EyeTrackerSession"] = None,
        paradigm_service: Optional["ParadigmService"] = None,
        skip_calibrate: bool = False,
    ) -> None:
        """
        初始化主窗口

        Args:
            dry_run: 是否为演示模式
            et_session: 眼动仪会话（可选）
            paradigm_service: 范式服务（可选）
            skip_calibrate: 是否跳过校准
        """
        super().__init__()

        self.dry_run = dry_run
        self.skip_calibrate = skip_calibrate

        # 设置窗口属性
        title = "眼动仪数据采集系统"
        if dry_run:
            title += " (演示模式)"
        self.setWindowTitle(title)

        # 设置窗口大小 - 可缩放
        self.setMinimumSize(800, 600)  # 最小尺寸
        self.resize(1200, 900)  # 默认尺寸

        self.setStyleSheet("background-color: #fafafa;")

        # 核心组件 - 从外部传入
        self.et_session = et_session
        self.paradigm_service = paradigm_service

        # 用户信息
        self.user_info: dict | None = None
        self.assessment_data: dict | None = None

        # Presenters (MVP架构)
        self.home_presenter = HomePresenter()
        self.assessment_presenter = AssessmentPresenter()

        # 页面管理
        self.home_page: HomePage | None = None
        self.assessment_page: AssessmentPage | None = None

        # 实验Presenter和播放窗口
        self.experiment_presenter: ExperimentPresenter | None = None
        self.playback_window: PlaybackWindow | None = None

        # 初始化
        if not dry_run and self.et_session:
            self.et_session.apply_eyetracker()
        else:
            print("🎯 演示模式：跳过眼动仪初始化")

        self._init_ui()
        self._init_experiment_presenter()
        self._init_playback_window()

    def _init_ui(self) -> None:
        """初始化用户界面"""
        # 创建主页面，传入Presenter
        self.home_page = HomePage(presenter=self.home_presenter)
        self.assessment_page = AssessmentPage(presenter=self.assessment_presenter)
        
        # 设置对象名称以便调试
        self.home_page.setObjectName("HomePage")
        self.assessment_page.setObjectName("AssessmentPage")
        
        # 设置初始页面
        self.setCentralWidget(self.home_page)

        # 连接信号
        self.home_page.page_finished.connect(self._on_info_completed)
        self.assessment_page.page_finished.connect(self._on_assessment_completed)
        self.assessment_page.back_clicked.connect(self._on_assessment_back)

    def _init_experiment_presenter(self) -> None:
        """初始化实验Presenter"""
        if not self.paradigm_service:
            print("❌ 范式服务未初始化，无法创建实验Presenter")
            return

        self.experiment_presenter = ExperimentPresenter(
            self.paradigm_service.collector,
            self.paradigm_service
        )

    def _init_playback_window(self) -> None:
        """初始化播放窗口"""
        if not self.experiment_presenter:
            print("❌ 实验Presenter未初始化，无法创建播放窗口")
            return

        self.playback_window = PlaybackWindow(self.experiment_presenter)

        # 连接播放完成信号
        self.playback_window.playback_finished.connect(self._on_playback_finished)
        # 连接播放停止信号（用户ESC）
        self.playback_window.playback_stopped.connect(self._on_playback_stopped)

    def _on_info_completed(self, user_info: dict) -> None:
        """基本信息录入完成"""
        try:
            # 验证已在页面中完成，直接保存数据
            self.home_presenter.save_user_info(user_info)
            self.user_info = user_info
            print(f"👤 用户信息: {user_info}")
            
            # 清空HomePage表单
            if self.home_page:
                self.home_page.clear_form()
            
            # 切换到评估页面
            if self._is_assessment_page_valid():
                # 恢复评估页面数据（如果有）
                if self.assessment_data:
                    print(f"🔄 恢复评估数据: {self.assessment_data}")
                    self.assessment_page.restore_forms(self.assessment_data)
                self.setCentralWidget(self.assessment_page)
            else:
                print("❌ AssessmentPage无效，重新创建")
                self.assessment_page = AssessmentPage(presenter=self.assessment_presenter)
                self.assessment_page.setObjectName("AssessmentPage")
                self.assessment_page.page_finished.connect(self._on_assessment_completed)
                self.assessment_page.back_clicked.connect(self._on_assessment_back)
                # 恢复评估页面数据（如果有）
                if self.assessment_data:
                    print(f"🔄 恢复评估数据: {self.assessment_data}")
                    self.assessment_page.restore_forms(self.assessment_data)
                self.setCentralWidget(self.assessment_page)
                
        except Exception as e:
            print(f"❌ 处理用户信息失败: {e}")
            self.home_page.show_error(f"处理用户信息失败: {e}")

    def _on_assessment_back(self) -> None:
        """评估页面返回按钮点击事件"""
        print("🔙 从评估页面返回信息录入页面")
        
        # 从Presenter获取已保存的评估数据（如果有的话）
        try:
            # 获取完整的评估数据
            full_assessment_data = self.assessment_presenter.get_assessment_data()
            if full_assessment_data:
                self.assessment_data = full_assessment_data
                print(f"✅ 评估数据已更新: {full_assessment_data}")
            else:
                print("ℹ️ 没有评估数据需要更新")
                
        except Exception as e:
            print(f"⚠️ 获取评估数据失败: {e}")
            # 继续执行，不阻止返回操作
        
        # 检查HomePage是否有效
        if self._is_home_page_valid():
            # 恢复HomePage数据
            if self.user_info:
                self.home_page.restore_form(self.user_info)
            # 切换到信息录入页面
            self.setCentralWidget(self.home_page)
        else:
            print("❌ HomePage无效，重新创建")
            self.home_page = HomePage(presenter=self.home_presenter)
            self.home_page.setObjectName("HomePage")
            self.home_page.page_finished.connect(self._on_info_completed)
            # 恢复HomePage数据
            if self.user_info:
                self.home_page.restore_form(self.user_info)
            self.setCentralWidget(self.home_page)
        # 注意：不要手动删除assessment_page，让Qt管理

    def _on_assessment_completed(self, assessment_data: dict) -> None:
        """评估数据录入完成"""
        try:
            # 获取评估数据
            mmse_data = assessment_data.get("mmse", {})
            moca_data = assessment_data.get("moca", {})
            
            # 验证已在页面中完成，直接保存数据
            self.assessment_presenter.save_assessment_data(mmse_data, moca_data)

            # 获取完整的评估数据
            full_assessment_data = self.assessment_presenter.get_assessment_data()
            self.assessment_data = full_assessment_data
            
            print(f"📝 评估数据: {full_assessment_data}")
            
            # 保存完整的受试者信息和评估记录
            self._save_subject_and_assessment_data()
            
            # 合并用户信息和评估数据
            if self.user_info:
                self.user_info.update(full_assessment_data)
            else:
                self.user_info = full_assessment_data

            # 开始实验流程
            if self.dry_run or self.skip_calibrate:
                self._skip_calibration(self.skip_calibrate)
            else:
                self._run_calibration()
                
        except Exception as e:
            print(f"❌ 处理评估数据失败: {e}")
            self.assessment_page.show_error(f"处理评估数据失败: {e}")

    def _save_subject_and_assessment_data(self) -> None:
        """保存受试者信息和评估记录"""
        try:
            if not self.user_info:
                print("⚠️ 没有用户信息可保存")
                return
                
            if not self.assessment_data:
                print("⚠️ 没有评估数据可保存")
                return
            
            # 创建完整的记录
            complete_record = {
                "subject_info": self.user_info.copy(),
                "assessment_record": self.assessment_data.copy(),
                "timestamp": datetime.now().isoformat(),
                "session_id": f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }
            
            print(f"💾 保存完整记录:")
            print(f"   - 受试者信息: {complete_record['subject_info']}")
            print(f"   - 评估记录: {complete_record['assessment_record']}")
            print(f"   - 时间戳: {complete_record['timestamp']}")
            print(f"   - 会话ID: {complete_record['session_id']}")
            
            # 这里可以添加实际的文件保存逻辑
            # 例如保存到JSON文件、数据库等
            self._save_to_file(complete_record)
            
            print("✅ 受试者信息和评估记录保存成功")
            
        except Exception as e:
            print(f"❌ 保存受试者信息和评估记录失败: {e}")
            import traceback
            traceback.print_exc()

    def _save_to_file(self, record: dict) -> None:
        """保存记录到文件"""
        try:
            import json
            import os
            from pathlib import Path
            
            # 创建数据目录
            data_dir = Path("data")
            data_dir.mkdir(exist_ok=True)
            
            # 创建受试者目录
            subject_id = record["subject_info"].get("name", "unknown")
            subject_dir = data_dir / subject_id
            subject_dir.mkdir(exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"assessment_{timestamp}.json"
            filepath = subject_dir / filename
            
            # 保存到JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(record, f, ensure_ascii=False, indent=2)
            
            print(f"📁 记录已保存到: {filepath}")
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            import traceback
            traceback.print_exc()

    def _run_calibration(self) -> None:
        """运行眼动仪校准"""
        try:
            print("🎯 开始校准...")
            if self.et_session is None:
                raise ValueError("眼动仪会话未初始化")
            self.et_session.start_calibration()
            print("✅ 校准成功")

            # 校准成功后，显示播放窗口
            self.show_playback_window()

        except Exception as e:
            print(f"❌ 校准过程出错: {e}")

    def _skip_calibration(self, skip_calibrate: bool = False) -> None:
        """运行演示校准"""
        if skip_calibrate:
            print("🎯 演示模式：跳过校准...")
        else:
            print("🎯 跳过校准，开始播放")

        # 提示用户校准完成
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("校准完成")
        msg_box.setText("校准完成，点击确定开始播放")
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.setStyleSheet(
            """
            QMessageBox {
                background-color: white;
                color: black;
            }
            QMessageBox QLabel {
                background-color: white;
                color: #333333;
            }
            QMessageBox QPushButton {
                background-color: #409eff;
                color: white;
                border: none;
                padding: 6px 20px;
                border-radius: 4px;
                min-width: 60px;
            }
            QMessageBox QPushButton:hover {
                background-color: #66b1ff;
            }
            QMessageBox QPushButton:pressed {
                background-color: #3a8ee6;
            }
        """
        )
        msg_box.exec()
        print("✅ 校准完成，开始播放")

        # 校准完成后，显示播放窗口
        self.show_playback_window()

    def show_playback_window(self) -> None:
        """显示独立的播放窗口，并启动范式流程"""
        print("🚀 启动范式播放流程...")

        if not self.playback_window:
            print("❌ 播放窗口未初始化")
            return

        # 隐藏主窗口
        print("🏠 隐藏主窗口")
        self.hide()

        if self.user_info:
            user_name = self.user_info.get("name", "unknown")
        else:
            user_name = "unknown"
        now_str = datetime.now().strftime("%y%m%d%H%M%S")
        session_name = f"{now_str}_{user_name}"

        # 开始播放（播放窗口内部会处理显示和范式服务的所有操作）
        if not self.playback_window.start_playback(session_name, self.user_info):
            print("❌ 启动播放失败")
            self._show_error_message("启动播放失败，请检查配置和设备连接。")
            self._restore_main_window()
            return

        print("✅ 播放窗口已显示，范式运行中...")

    def _on_playback_finished(self, results: dict) -> None:
        """播放正常完成后的回调"""
        print("✅ 播放完成，处理结果...")
        
        # 检查是否需要重新采集（数据质量不合格）
        retry_requested = results.get("retry_requested", False)
        
        if retry_requested:
            print("🔄 数据质量不合格，需要重新采集，恢复表单信息...")
            # 恢复主窗口
            self._restore_main_window()
            # 恢复表单数据
            if self._is_home_page_valid() and self.user_info:
                try:
                    self.home_page.restore_form(self.user_info)
                except RuntimeError as e:
                    print(f"⚠️ 恢复表单数据失败: {e}")
            return

        # 只有在正常完成且不重新采集时才清空表单
        if not retry_requested:
            # 清空Presenters中的数据
            self.home_presenter.clear_user_info()
            self.assessment_presenter.clear_assessment_data()
            
            # 清空表单
            if self._is_home_page_valid():
                try:
                    self.home_page.clear_form()
                except RuntimeError as e:
                    print(f"⚠️ HomePage已被删除，跳过清空表单: {e}")
                    self.home_page = None
            
        if results.get("success"):
            output_dir = results.get("output_dir", "未知位置")
            print(f"💾 结果已保存到: {output_dir}")
            self._show_completion_message(
                f"采集完成！\n数据已保存到:\n{output_dir}"
            )
        else:
            error_msg = results.get("error", "未知错误")
            print(f"❌ 保存数据失败: {error_msg}")
            self._show_error_message(f"保存数据失败: {error_msg}")

        self._restore_main_window()

    def _on_playback_stopped(self, results: dict) -> None:
        """播放被用户中断后的回调"""
        print("⏹️ 用户停止播放，处理结果...")
        
        # 检查是否需要重新采集（数据质量不合格）
        retry_requested = results.get("retry_requested", False)
        
        if retry_requested:
            print("🔄 数据质量不合格，需要重新采集，恢复表单信息...")
            # 恢复主窗口
            self._restore_main_window()
            # 恢复表单数据
            if self._is_home_page_valid() and self.user_info:
                try:
                    self.home_page.restore_form(self.user_info)
                except RuntimeError as e:
                    print(f"⚠️ 恢复表单数据失败: {e}")
            return

        # 只有在不重新采集时才清空表单
        if not retry_requested:
            # 清空Presenters中的数据
            self.home_presenter.clear_user_info()
            self.assessment_presenter.clear_assessment_data()
            
            # 清空表单
            if self._is_home_page_valid():
                try:
                    self.home_page.clear_form()
                except RuntimeError as e:
                    print(f"⚠️ HomePage已被删除，跳过清空表单: {e}")
                    self.home_page = None
            
        if results.get("success"):
            output_dir = results.get("output_dir", "未知位置")
            print(f"💾 中断结果已保存到: {output_dir}")
            self._show_completion_message(
                f"采集已中断。\n\n数据已保存到:\n{output_dir}"
            )
        else:
            error_msg = results.get("error", "未知错误")
            print(f"❌ 保存数据失败: {error_msg}")
            self._show_error_message(f"保存数据失败: {error_msg}")

        self._restore_main_window()

    def _restore_main_window(self) -> None:
        """恢复主窗口显示"""
        # 确保显示HomePage
        if self._is_home_page_valid():
            self.setCentralWidget(self.home_page)
        else:
            print("❌ HomePage无效，重新创建")
            self.home_page = HomePage(presenter=self.home_presenter)
            self.home_page.setObjectName("HomePage")
            self.home_page.page_finished.connect(self._on_info_completed)
            self.setCentralWidget(self.home_page)
        
        self.show()
        self.raise_()
        self.activateWindow()
        print("🏠 主窗口已恢复显示")

    def _show_completion_message(self, message: str) -> None:
        """显示操作完成消息"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("操作完成")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.setStyleSheet(
            """
            QMessageBox { background-color: white; color: black; }
            QMessageBox QLabel { background-color: white; color: #333; }
            QMessageBox QPushButton { background-color: #409eff; color: white; border: none; padding: 6px 20px; border-radius: 4px; }
            QMessageBox QPushButton:hover { background-color: #66b1ff; }
        """
        )
        msg_box.exec()

    def _show_error_message(self, message: str) -> None:
        """显示错误消息"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("错误")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setStyleSheet(
            """
            QMessageBox { background-color: white; color: black; }
            QMessageBox QLabel { background-color: white; color: #333; }
            QMessageBox QPushButton { background-color: #f56c6c; color: white; border: none; padding: 6px 20px; border-radius: 4px; }
            QMessageBox QPushButton:hover { background-color: #f78989; }
        """
        )
        msg_box.exec()

    def set_paradigm_service(self, paradigm_service: "ParadigmService") -> None:
        """设置范式服务"""
        self.paradigm_service = paradigm_service

    def _is_home_page_valid(self) -> bool:
        """检查HomePage对象是否有效"""
        if not self.home_page:
            return False
        try:
            # 尝试访问一个简单的属性来检查对象是否有效
            _ = self.home_page.objectName()
            return True
        except RuntimeError as e:
            print(f"⚠️ HomePage对象已被删除: {e}")
            self.home_page = None
            return False

    def _is_assessment_page_valid(self) -> bool:
        """检查AssessmentPage对象是否有效"""
        if not self.assessment_page:
            return False
        try:
            # 尝试访问一个简单的属性来检查对象是否有效
            _ = self.assessment_page.objectName()
            return True
        except RuntimeError as e:
            print(f"⚠️ AssessmentPage对象已被删除: {e}")
            self.assessment_page = None
            return False

    def closeEvent(self, event: QCloseEvent) -> None:
        """窗口关闭事件"""
        print("👋 正在关闭应用程序...")

        # 关闭播放窗口
        if self.playback_window:
            self.playback_window.close()

        if self.et_session:
            print("🔌 断开眼动仪连接")

        event.accept()
