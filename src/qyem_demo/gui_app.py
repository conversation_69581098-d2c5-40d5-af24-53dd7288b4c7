"""
GUI应用 - 图形界面模式实现
"""

import argparse
import sys
from typing import Any

from PySide6.QtWidgets import QApplication

from .base_app import BaseApp
from .ui.main_window import MainWindow


class GuiApp(BaseApp):
    """GUI应用"""

    def __init__(self, dry_run: bool = False):
        """
        初始化GUI应用

        Args:
            dry_run: 是否为演示模式
        """
        super().__init__(dry_run)

        # Qt组件
        self.app: QApplication | None = None
        self.main_window: MainWindow | None = None

        print("🎨 GUI应用初始化完成")

    def run(self, args: argparse.Namespace) -> int:
        """
        运行GUI应用

        Args:
            args: 命令行参数

        Returns:
            退出代码（0表示成功）
        """
        try:
            print("🎨 启动GUI模式")
            print("=" * 50)

            # 1. 创建Qt应用
            self.app = QApplication(sys.argv if not QApplication.instance() else [])

            # 2. 加载配置
            if not self._load_config(args.config):
                return 1

            # 3. 连接眼动仪
            if not self._connect_eyetracker():
                return 1

            # 5. 应用许可证
            if hasattr(args, "license") and args.license:
                if not self._apply_license(args.license):
                    return 1
            else:
                # 如果未手动指定许可证，则自动应用许可证
                if not self._auto_apply_license():
                    print("⚠️ 自动许可证应用失败，但继续执行")
                    return 1

            # 5. 初始化核心组件
            self._initialize_core_components()

            # 6. 创建主窗口
            self.main_window = MainWindow(
                dry_run=self.dry_run,
                et_session=self.et_session,
                paradigm_service=self.paradigm_service,
                skip_calibrate=getattr(args, "skip_calibrate", False),
            )

            # 7. 显示主窗口
            self.main_window.show()

            # 8. 运行Qt事件循环
            exit_code = int(self.app.exec())

            print("✅ GUI应用正常退出")
            return exit_code

        except Exception as e:
            print(f"💥 GUI应用发生错误: {e}")
            return 1
        finally:
            self._cleanup()

    def start_calibration(self) -> bool:
        """
        启动校准程序（可被GUI调用）

        Returns:
            是否校准成功
        """
        return self._run_calibration()

    def get_status(self) -> dict[str, Any]:
        """
        获取应用状态

        Returns:
            包含应用状态信息的字典
        """
        eyetracker_connected = False
        if not self.dry_run and self.et_session:
            eyetracker_connected = self.et_session.has_device()

        return {
            "eyetracker_connected": eyetracker_connected,
            "collector_ready": self.collector is not None,
            "paradigm_ready": self.paradigm_service is not None,
            "dry_run": self.dry_run,
            "mode": "gui",
        }
